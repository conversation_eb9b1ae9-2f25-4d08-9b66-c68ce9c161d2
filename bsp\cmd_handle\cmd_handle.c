#include "GPIO.h"
#include "User_Driver.h"
#include "VI4302_System.h"
#include "VI4302_handle.h"
#include "WDT.h"
#include "_cmd_handle.h"
#include "adc.h"
#include "converter.h"
#include "data_handle.h"
#include "delay.h"
#include "flash.h"
#include "timer.h"
#include "uartfifo.h"
#include "usart.h"
#include "work_mode.h"


const uint8_t BANBEN_NumberBuff[7] = {25, 7, 1, 9, 0, 0, 2};

//	执行串口的具体指令
#define READ_HIST_BUF_MAXSIZE 2048

#pragma pack(4)
__IO uint8_t histogram_data[READ_HIST_BUF_MAXSIZE] = {
    1,
};
#pragma pack(1)


void ALL_ParaInit(void) {
    g_ControlPara.vi4302_work_mode = _RANG_MODE;
}


//主通信接口或者RS485接口
void UART_AllHandle(void) {
    uint8_t  _uart_send_pbuff[UARTOUTFIFO_LEN] = {0x00};
    uint16_t i                                 = 0;

    if (uart1infifo_HaveData()) {
        while (uart1infifo_HaveData()) {
            _uart_send_pbuff[i++] = uart1infifo_DataOut();

            if (UARTOUTFIFO_LEN <= i) {
                break;
            }

            WDT_Week();
        }

        //串口数据处理
        //		YC_UART_AllHandle(_uart_send_pbuff, i);       //亿辰串口数据处理方式（数据长度占1个字节）
        XSJ_UART_AllHandle(_uart_send_pbuff, i);  //芯视界串口数据处理方式（数据长度占2个字节）

        WDT_Week();
    }
}

//---------------------------------------------------------------------
//函数介绍：串口处理
//---------------------------------------------------------------------
void XSJ_UART_AllHandle(uint8_t *pbuff, uint16_t pbuff_len) {
    uint16_t i = 0, j = 0;
    uint8_t  uart_val                            = 0;
    uint8_t  valid_flag                          = 0;
    uint16_t _pbuff_len                          = 0;
    uint8_t  g_UartHandleBuffer[UARTINFIFO_SIZE] = {0};

    while (0 < pbuff_len) {
        uart_val = pbuff[j];

        //*************	私有 协议解析 start 		*************//
        if (valid_flag == 0)  //没找到帧头
        {
            if (uart_val == 0x5A)  //找到了帧头
            {
                valid_flag              = 1;
                i                       = 0;
                g_UartHandleBuffer[i++] = uart_val;
            } else {
                i = 0;
            }
        } else if (valid_flag == 1)  //已经找到了帧头
        {
            g_UartHandleBuffer[i++] = uart_val;

            if (i >= 4)  //根据协议长度区判断接收的协议帧大小
            {
                _pbuff_len = ((g_UartHandleBuffer[3] & 0x00ff) << 8) + g_UartHandleBuffer[2];

                if ((_pbuff_len + 5) >= sizeof(g_UartHandleBuffer)) {
                    return;
                } else if (i == (_pbuff_len + 5))  //一包结束
                {
                    //串口数据包处理
                    XSJ_DealProtocol(g_UartHandleBuffer);

                    i          = 0;
                    valid_flag = 0;
                }
            }
        }

        //*************	私有 协议解析 end 		*************//
        pbuff_len--;
        j++;
    }
}

//---------------------------------------------------------------------
//函数介绍：协议处理
//---------------------------------------------------------------------
uint8_t opt_code       = 0;
uint8_t histogram_type = 0;

uint8_t XSJ_DealProtocol(uint8_t *pd) {
    static uint8_t pixel      = 0;
    uint8_t        ret        = 0;
    uint8_t        return_res = RET_OK;
    uint16_t       i = 0, j = 0, k = 0;
    uint16_t       payload_len                       = 0;
    uint8_t        _uart_send_pbuff[UARTOUTFIFO_LEN] = {0x00};
    uint16_t       peak_data[25]                     = {0};
    uint16_t       distance_value                    = 0;
    const uint8_t *payload                           = &pd[4];  //获取PAYLOAD数据区数据

    payload_len = le_to_host16u(&pd[2]);
    opt_code    = pd[1];  //获取OP_CODE操作码

#if 0  //上位机未加校验功能，临时关闭
	if(pd[_pd_len+3] != CMD_SumCheck(pd,_pd_len+3))  //校验不正确，直接返回
	{
		return_res = RET_FAIL;
		
		return return_res;
	}
#endif

    switch (pd[1]) {
    case USER_MP_INFO_CMD:  //单pixel模式
    {
        if (g_ControlPara.vi4302_work_mode != _SINGLE_PIXEL_MODE) {
            opt_code = USER_RANGING_CMD;

            TX_Disen();
            vi4302_HW_set_demo();
            g_ControlPara.vi4302_work_mode = _SINGLE_PIXEL_MODE;
            VI4302_AllInit();
        }

        VI4302_SinglePixel_Output((uint16_t *)histogram_data);
        UART_CmdAckSend(USER_MP_INFO_CMD, (uint8_t *)histogram_data, (25) * 2);
    } break;

    case USER_REG_R_CMD:  //读取寄存器
    {
        uint16_t addr = 0;
        uint8_t  val  = 0;

        addr = ((pd[4]) | (pd[5] << 8));
        val  = vi4302_read_register(addr);
        UART_CmdAckSend(USER_REG_R_CMD, (uint8_t *)&val, 1);
    } break;

    case USER_REG_W_CMD:  //写寄存器
    {
        uint16_t addr = 0;
        uint16_t val  = 0;

        addr = ((pd[4]) | (pd[5] << 8));
        val  = pd[6];
        vi4302_write_register(addr, val);
    } break;

    case USER_HIST_R_CMD:  //读取直方图
    {
        histogram_type = pd[4];
        uint8_t ret    = VI4302_Read_His_Config(histogram_type);  //#

        if (ret != SUCCESS) {
            UART_CmdAckSend(0, &ret, 1);
        }
    } break;

    case USER_RANGING_CMD:  //开始测距
    {

        //			if(g_sys_cfg.work_mode != _RANG_MODE	)
        //			{
        //				g_sys_cfg.work_mode = _RANG_MODE;
        //				GD32FLASH_Write(FLASH_ADDR_System_Config, (uint8_t*)&g_sys_cfg, sizeof ( g_sys_cfg ));
        //				NVIC_SystemReset();
        //			}

        uint8_t ret = 0;

        if (pd[4] > 0) {
            ret = VI4302_Start_Ranging();
        } else {
            ret = VI4302_Stop_Ranging();
        }

        if (ret != 0x11) {
            UART_CmdAckSend(0, &ret, 1);
        }
    } break;


    default:
        break;
    }
    return return_res;
}

//---------------------------------------------------------------------
//函数介绍：串口处理
//---------------------------------------------------------------------
void YC_UART_AllHandle(uint8_t *pbuff, uint16_t pbuff_len) {
    uint16_t i = 0, j = 0;
    uint8_t  uart_val                            = 0;
    uint8_t  valid_flag                          = 0;
    uint16_t _pbuff_len                          = 0;
    uint8_t  g_UartHandleBuffer[UARTINFIFO_SIZE] = {0};

    while (0 < pbuff_len) {
        uart_val = pbuff[j];

        //*************	私有 协议解析 start 		*************//
        if (valid_flag == 0)  //没找到帧头
        {
            if (uart_val == 0x5A)  //找到了帧头
            {
                valid_flag              = 1;
                i                       = 0;
                g_UartHandleBuffer[i++] = uart_val;
            } else {
                i = 0;
            }
        } else if (valid_flag == 1)  //已经找到了帧头
        {
            g_UartHandleBuffer[i++] = uart_val;

            if (i >= 3)  //根据协议长度区判断接收的协议帧大小
            {
                _pbuff_len = g_UartHandleBuffer[2];

                if ((_pbuff_len + 4) >= sizeof(g_UartHandleBuffer)) {
                    return;
                } else if (i == (_pbuff_len + 4))  //一包结束
                {
                    YC_DealProtocol(g_UartHandleBuffer);

                    i          = 0;
                    valid_flag = 0;
                }
            }
        }

        //*************	私有 协议解析 end 		*************//
        pbuff_len--;
        j++;
    }
}

//---------------------------------------------------------------------
//函数介绍：协议处理
//---------------------------------------------------------------------
uint8_t YC_DealProtocol(uint8_t *pd) {
    uint8_t  ret        = 0;
    uint8_t  return_res = RET_OK;
    uint16_t i = 0, j = 0, k = 0;
    uint16_t _pd_len                           = 0;
    uint8_t  _uart_send_pbuff[UARTOUTFIFO_LEN] = {0x00};
    uint16_t peak_data[25]                     = {0};
    uint16_t distance_value                    = 0;

    _pd_len = pd[2];

    if (pd[_pd_len + 3] != CMD_SumCheck(pd, _pd_len + 3))  //校验不正确，直接返回
    {
        return_res = RET_FAIL;

        return return_res;
    }

    switch (pd[1]) {
    case FIRMWARE_BANBEN_READ_CMD:  //固件版本获取命令
        i                     = 0;
        _uart_send_pbuff[i++] = BANBEN_NumberBuff[0];
        _uart_send_pbuff[i++] = BANBEN_NumberBuff[1];
        _uart_send_pbuff[i++] = BANBEN_NumberBuff[2];
        _uart_send_pbuff[i++] = BANBEN_NumberBuff[3];
        _uart_send_pbuff[i++] = BANBEN_NumberBuff[4];
        _uart_send_pbuff[i++] = BANBEN_NumberBuff[5];
        _uart_send_pbuff[i++] = BANBEN_NumberBuff[6];
        YC_UART_CmdAckSend(FIRMWARE_BANBEN_READ_CMD, _uart_send_pbuff, i);

        break;

    case VI430X_RANGING_SET_CMD:
        if (pd[3] == _RANG_MODE)  //开始测距
        {
            if (g_ControlPara.vi4302_work_mode == _SINGLE_PIXEL_MODE) {
                TX_Disen();
                vi4302_HW_set_demo();
                g_ControlPara.vi4302_work_mode = _RANG_MODE;
                VI4302_AllInit();
                ret = VI4302_Start_Ranging();
            }

            g_ControlPara.vi4302_ranging_state = 1;
            //				ret = VI4302_Start_Ranging();
        } else {
            if (g_ControlPara.vi4302_work_mode == _SINGLE_PIXEL_MODE)  //
            {
                TX_Disen();
                vi4302_HW_set_demo();
                g_ControlPara.vi4302_work_mode = _RANG_MODE;
                VI4302_AllInit();
            }

            //				//结束测距
            //				ret = VI4302_Stop_Ranging();
            g_ControlPara.vi4302_ranging_state = 0;
        }

        i                     = 0;
        _uart_send_pbuff[i++] = g_ControlPara.vi4302_work_mode;
        YC_UART_CmdAckSend(VI430X_RANGING_SET_CMD, _uart_send_pbuff, i);
        break;

    case VI430X_TEMP_PARA_SET_CMD:
        g_fmNeedSaved.wendu_cal_para[0] = pd[3] + ((pd[4] & 0x00fff) << 8);
        g_fmNeedSaved.wendu_cal_para[1] = pd[5] + ((pd[6] & 0x00fff) << 8);
        flash_SaveFmData();
        i                     = 0;
        _uart_send_pbuff[i++] = g_fmNeedSaved.wendu_cal_para[0];
        _uart_send_pbuff[i++] = g_fmNeedSaved.wendu_cal_para[0] >> 8;
        _uart_send_pbuff[i++] = g_fmNeedSaved.wendu_cal_para[1];
        _uart_send_pbuff[i++] = g_fmNeedSaved.wendu_cal_para[1] >> 8;
        YC_UART_CmdAckSend(VI430X_TEMP_PARA_SET_CMD, _uart_send_pbuff, i);
        break;

    case VI430X_TEMP_PARA_GET_CMD:
        flash_ReadFmData();
        i                     = 0;
        _uart_send_pbuff[i++] = g_fmNeedSaved.wendu_cal_para[0];
        _uart_send_pbuff[i++] = g_fmNeedSaved.wendu_cal_para[0] >> 8;
        _uart_send_pbuff[i++] = g_fmNeedSaved.wendu_cal_para[1];
        _uart_send_pbuff[i++] = g_fmNeedSaved.wendu_cal_para[1] >> 8;
        YC_UART_CmdAckSend(VI430X_TEMP_PARA_GET_CMD, _uart_send_pbuff, i);
        break;

    case VI430X_CALA_PARA1_SET_CMD:
        g_fmNeedSaved.gaofan_quanju_en      = pd[3];
        g_fmNeedSaved.heibai_fanduan_number = pd[4];
        for (i = 0; i < 5; i++) {
            g_fmNeedSaved.heibai_quanju_para[i] = pd[5 + 2 * i] + ((pd[6 + 2 * i] & 0x00ff) << 8);
        }
        for (i = 0; i < 4; i++) {
            g_fmNeedSaved.gaofan_quanju_para[i] = pd[15 + 2 * i] + ((pd[16 + 2 * i] & 0x00ff) << 8);
        }
        flash_SaveFmData();

        i                     = 0;
        _uart_send_pbuff[i++] = g_fmNeedSaved.gaofan_quanju_en;
        _uart_send_pbuff[i++] = g_fmNeedSaved.heibai_fanduan_number;
        for (j = 0; j < 5; j++) {
            _uart_send_pbuff[i++] = g_fmNeedSaved.heibai_quanju_para[j];
            _uart_send_pbuff[i++] = g_fmNeedSaved.heibai_quanju_para[j] >> 8;
        }
        for (j = 0; j < 4; j++) {
            _uart_send_pbuff[i++] = g_fmNeedSaved.gaofan_quanju_para[j];
            _uart_send_pbuff[i++] = g_fmNeedSaved.gaofan_quanju_para[j] >> 8;
        }

        YC_UART_CmdAckSend(VI430X_CALA_PARA1_SET_CMD, _uart_send_pbuff, i);
        break;

    case VI430X_CALA_PARA2_SET_CMD:
        for (j = 0; j < 9; j++) {
            for (i = 0; i < 5; i++) {
                g_fmNeedSaved.heibai_fenduan_para[j][i] = pd[3 + 2 * i + 26 * j] + ((pd[4 + 2 * i + 26 * j] & 0x00ff) << 8);
            }
            for (i = 0; i < 8; i++) {
                g_fmNeedSaved.gaofan_fenduan_para[j][i] = pd[13 + 2 * i + 26 * j] + ((pd[14 + 2 * i + 26 * j] & 0x00ff) << 8);
            }
        }
        flash_SaveFmData();

        i = 0;
        for (j = 0; j < 9; j++) {
            for (k = 0; k < 5; k++) {
                _uart_send_pbuff[i++] = g_fmNeedSaved.heibai_fenduan_para[j][k];
                _uart_send_pbuff[i++] = g_fmNeedSaved.heibai_fenduan_para[j][k] >> 8;
            }
            for (k = 0; k < 8; k++) {
                _uart_send_pbuff[i++] = g_fmNeedSaved.gaofan_fenduan_para[j][k];
                _uart_send_pbuff[i++] = g_fmNeedSaved.gaofan_fenduan_para[j][k] >> 8;
            }
        }

        YC_UART_CmdAckSend(VI430X_CALA_PARA2_SET_CMD, _uart_send_pbuff, i);
        break;

    case VI430X_CALA_PARA3_SET_CMD:
        for (j = 0; j < 9; j++) {
            for (i = 0; i < 5; i++) {
                g_fmNeedSaved.heibai_fenduan_para[9 + j][i] = pd[3 + 2 * i + 26 * j] + ((pd[4 + 2 * i + 26 * j] & 0x00ff) << 8);
            }
            for (i = 0; i < 8; i++) {
                g_fmNeedSaved.gaofan_fenduan_para[9 + j][i] = pd[13 + 2 * i + 26 * j] + ((pd[14 + 2 * i + 26 * j] & 0x00ff) << 8);
            }
        }
        flash_SaveFmData();

        i = 0;
        for (j = 9; j < 18; j++) {
            for (k = 0; k < 5; k++) {
                _uart_send_pbuff[i++] = g_fmNeedSaved.heibai_fenduan_para[j][k];
                _uart_send_pbuff[i++] = g_fmNeedSaved.heibai_fenduan_para[j][k] >> 8;
            }
            for (k = 0; k < 8; k++) {
                _uart_send_pbuff[i++] = g_fmNeedSaved.gaofan_fenduan_para[j][k];
                _uart_send_pbuff[i++] = g_fmNeedSaved.gaofan_fenduan_para[j][k] >> 8;
            }
        }

        YC_UART_CmdAckSend(VI430X_CALA_PARA3_SET_CMD, _uart_send_pbuff, i);
        break;

    case VI430X_CALA_PARA4_SET_CMD:
        for (j = 0; j < 9; j++) {
            for (i = 0; i < 5; i++) {
                g_fmNeedSaved.heibai_fenduan_para[18 + j][i] = pd[3 + 2 * i + 26 * j] + ((pd[4 + 2 * i + 26 * j] & 0x00ff) << 8);
            }
            for (i = 0; i < 8; i++) {
                g_fmNeedSaved.gaofan_fenduan_para[18 + j][i] = pd[13 + 2 * i + 26 * j] + ((pd[14 + 2 * i + 26 * j] & 0x00ff) << 8);
            }
        }
        flash_SaveFmData();

        i = 0;
        for (j = 18; j < 27; j++) {
            for (k = 0; k < 5; k++) {
                _uart_send_pbuff[i++] = g_fmNeedSaved.heibai_fenduan_para[j][k];
                _uart_send_pbuff[i++] = g_fmNeedSaved.heibai_fenduan_para[j][k] >> 8;
            }
            for (k = 0; k < 8; k++) {
                _uart_send_pbuff[i++] = g_fmNeedSaved.gaofan_fenduan_para[j][k];
                _uart_send_pbuff[i++] = g_fmNeedSaved.gaofan_fenduan_para[j][k] >> 8;
            }
        }

        YC_UART_CmdAckSend(VI430X_CALA_PARA4_SET_CMD, _uart_send_pbuff, i);
        break;

    case VI430X_CALA_PARA5_SET_CMD:
        for (j = 0; j < 3; j++) {
            for (i = 0; i < 5; i++) {
                g_fmNeedSaved.heibai_fenduan_para[27 + j][i] = pd[3 + 2 * i + 26 * j] + ((pd[4 + 2 * i + 26 * j] & 0x00ff) << 8);
            }
            for (i = 0; i < 8; i++) {
                g_fmNeedSaved.gaofan_fenduan_para[27 + j][i] = pd[13 + 2 * i + 26 * j] + ((pd[14 + 2 * i + 26 * j] & 0x00ff) << 8);
            }
        }
        flash_SaveFmData();

        i = 0;
        for (j = 27; j < 30; j++) {
            for (k = 0; k < 5; k++) {
                _uart_send_pbuff[i++] = g_fmNeedSaved.heibai_fenduan_para[j][k];
                _uart_send_pbuff[i++] = g_fmNeedSaved.heibai_fenduan_para[j][k] >> 8;
            }
            for (k = 0; k < 8; k++) {
                _uart_send_pbuff[i++] = g_fmNeedSaved.gaofan_fenduan_para[j][k];
                _uart_send_pbuff[i++] = g_fmNeedSaved.gaofan_fenduan_para[j][k] >> 8;
            }
        }
        i = 26 * 9;
        YC_UART_CmdAckSend(VI430X_CALA_PARA5_SET_CMD, _uart_send_pbuff, i);
        break;

    case VI430X_CALA_PARA1_GET_CMD:
        flash_ReadFmData();
        i                     = 0;
        _uart_send_pbuff[i++] = g_fmNeedSaved.gaofan_quanju_en;
        _uart_send_pbuff[i++] = g_fmNeedSaved.heibai_fanduan_number;
        for (j = 0; j < 5; j++) {
            _uart_send_pbuff[i++] = g_fmNeedSaved.heibai_quanju_para[j];
            _uart_send_pbuff[i++] = g_fmNeedSaved.heibai_quanju_para[j] >> 8;
        }
        for (j = 0; j < 4; j++) {
            _uart_send_pbuff[i++] = g_fmNeedSaved.gaofan_quanju_para[j];
            _uart_send_pbuff[i++] = g_fmNeedSaved.gaofan_quanju_para[j] >> 8;
        }

        YC_UART_CmdAckSend(VI430X_CALA_PARA1_GET_CMD, _uart_send_pbuff, i);
        break;

    case VI430X_CALA_PARA2_GET_CMD:
        flash_ReadFmData();
        i = 0;
        for (j = 0; j < 9; j++) {
            for (k = 0; k < 5; k++) {
                _uart_send_pbuff[i++] = g_fmNeedSaved.heibai_fenduan_para[j][k];
                _uart_send_pbuff[i++] = g_fmNeedSaved.heibai_fenduan_para[j][k] >> 8;
            }
            for (k = 0; k < 8; k++) {
                _uart_send_pbuff[i++] = g_fmNeedSaved.gaofan_fenduan_para[j][k];
                _uart_send_pbuff[i++] = g_fmNeedSaved.gaofan_fenduan_para[j][k] >> 8;
            }
        }
        YC_UART_CmdAckSend(VI430X_CALA_PARA2_GET_CMD, _uart_send_pbuff, i);
        break;

    case VI430X_CALA_PARA3_GET_CMD:
        flash_ReadFmData();
        i = 0;
        for (j = 9; j < 18; j++) {
            for (k = 0; k < 5; k++) {
                _uart_send_pbuff[i++] = g_fmNeedSaved.heibai_fenduan_para[j][k];
                _uart_send_pbuff[i++] = g_fmNeedSaved.heibai_fenduan_para[j][k] >> 8;
            }
            for (k = 0; k < 8; k++) {
                _uart_send_pbuff[i++] = g_fmNeedSaved.gaofan_fenduan_para[j][k];
                _uart_send_pbuff[i++] = g_fmNeedSaved.gaofan_fenduan_para[j][k] >> 8;
            }
        }
        YC_UART_CmdAckSend(VI430X_CALA_PARA3_GET_CMD, _uart_send_pbuff, i);
        break;

    case VI430X_CALA_PARA4_GET_CMD:
        flash_ReadFmData();
        i = 0;
        for (j = 18; j < 27; j++) {
            for (k = 0; k < 5; k++) {
                _uart_send_pbuff[i++] = g_fmNeedSaved.heibai_fenduan_para[j][k];
                _uart_send_pbuff[i++] = g_fmNeedSaved.heibai_fenduan_para[j][k] >> 8;
            }
            for (k = 0; k < 8; k++) {
                _uart_send_pbuff[i++] = g_fmNeedSaved.gaofan_fenduan_para[j][k];
                _uart_send_pbuff[i++] = g_fmNeedSaved.gaofan_fenduan_para[j][k] >> 8;
            }
        }

        YC_UART_CmdAckSend(VI430X_CALA_PARA4_GET_CMD, _uart_send_pbuff, i);
        break;

    case VI430X_CALA_PARA5_GET_CMD:
        flash_ReadFmData();
        i = 0;
        for (j = 27; j < 30; j++) {
            for (k = 0; k < 5; k++) {
                _uart_send_pbuff[i++] = g_fmNeedSaved.heibai_fenduan_para[j][k];
                _uart_send_pbuff[i++] = g_fmNeedSaved.heibai_fenduan_para[j][k] >> 8;
            }
            for (k = 0; k < 8; k++) {
                _uart_send_pbuff[i++] = g_fmNeedSaved.gaofan_fenduan_para[j][k];
                _uart_send_pbuff[i++] = g_fmNeedSaved.gaofan_fenduan_para[j][k] >> 8;
            }
        }
        i = 26 * 9;
        YC_UART_CmdAckSend(VI430X_CALA_PARA5_GET_CMD, _uart_send_pbuff, i);
        break;

    case VI430X_PIXEL_GET_CMD:
        if (g_ControlPara.vi4302_work_mode != _SINGLE_PIXEL_MODE) {
            TX_Disen();
            vi4302_HW_set_demo();
            g_ControlPara.vi4302_work_mode = _SINGLE_PIXEL_MODE;
            VI4302_AllInit();
        }
        VI4302_SinglePixel_Output(peak_data);
        YC_UART_CmdAckSend(VI430X_PIXEL_GET_CMD, (uint8_t *)peak_data, 25 * 2);
        break;

    case UPDATA_CONFIG_CMD:
        if (pd[3] != 0x00)  //准备升级
        {
            g_snNeedSaved.updateProgramFlag = 0;
        } else {
            g_snNeedSaved.updateProgramFlag = UPDATE_UPDAT_SUCCESS_FLAG;
        }
        flash_SaveSnData();
        i                     = 0;
        _uart_send_pbuff[i++] = pd[3];
        YC_UART_CmdAckSend(UPDATA_CONFIG_CMD, _uart_send_pbuff, i);
        delay_ms(200);
        SYSTERM_Init();
        break;

    case UPDATA_DATA_CMD:
        break;

    case FACTORY_INIT_SET_CMD:
        break;

    case VI430X_offset_SET_CMD:
        distance_value                = ((pd[4] & 0x00ff) << 8) + pd[3];
        g_dataNeedSaved.point0_offset = distance_value - g_ControlPara.vi4302_tof_data_no_tiaoling_offset;

        flash_SaveConfigerData();
        i                     = 0;
        _uart_send_pbuff[i++] = distance_value;
        _uart_send_pbuff[i++] = distance_value >> 8;
        _uart_send_pbuff[i++] = g_dataNeedSaved.point0_offset;
        _uart_send_pbuff[i++] = g_dataNeedSaved.point0_offset >> 8;
        YC_UART_CmdAckSend(VI430X_offset_SET_CMD, _uart_send_pbuff, i);
        break;

    case VI430X_offset_GET_CMD:
        i                     = 0;
        _uart_send_pbuff[i++] = g_ControlPara.vi4302_tof_data;
        _uart_send_pbuff[i++] = g_ControlPara.vi4302_tof_data >> 8;
        _uart_send_pbuff[i++] = g_dataNeedSaved.point0_offset;
        _uart_send_pbuff[i++] = g_dataNeedSaved.point0_offset >> 8;
        YC_UART_CmdAckSend(VI430X_offset_GET_CMD, _uart_send_pbuff, i);
        break;

    default:
        break;
    }
    return return_res;
}


//执行串口的具体指令
void Execute_instruction(void) {
    uint8_t ranging_data[23] = {0};
    int16_t temperature_x100 = 0;
    uint8_t confidence;
    int16_t yuanshi_tof_distance = 0;

    switch (opt_code) {
    case USER_HIST_R_CMD: {
        uint8_t           uart_pbuff[128] = {0x5a, 0x00};
        uint8_t           cmd_sum         = 0;
        volatile uint16_t i = 0, j = 0, y = 0;

        if (gpio0_int_cnt > 0) {
            uint8_t check_sum;
            //	uint16_t offset=0,size=0;
            uart_pbuff[0] = 0x5A;
            cmd_sum += uart_pbuff[i++];
            uart_pbuff[i] = USER_HIST_R_CMD;
            cmd_sum += uart_pbuff[i++];
            uart_pbuff[i] = (HISTOGRAM_DATA_SIZE & 0x00ff);
            cmd_sum += uart_pbuff[i++];
            uart_pbuff[i] = (HISTOGRAM_DATA_SIZE >> 8);
            cmd_sum += uart_pbuff[i++];
            while (UART2_SendFlag)
                ;  //等待发送完成
            { UART2_SendData(uart_pbuff, i); }
            VI4302_Read_Histogram(histogram_type, (uint8_t *)histogram_data);
            while (UART2_SendFlag)
                ;  //等待发送完成
            { UART2_SendData((uint8_t *)histogram_data, HISTOGRAM_DATA_SIZE); }
            for (uint16_t i = 0; i < HISTOGRAM_DATA_SIZE; i++) {
                check_sum += histogram_data[i];
            }

            check_sum = (~check_sum);
            while (UART2_SendFlag)
                ;  //等待发送完成
            { UART2_SendData(&check_sum, 1); }
            gpio0_int_cnt = 0;
        }
    } break;

    case USER_RANGING_CMD: {
        if (g_ControlPara.vi4302_data_valid_flag != 0) {
            g_ControlPara.vi4302_data_valid_flag = 0;

            if (gpio0_int_cnt > 10)  // 10个点打印一次
            {
                temperature_x100 = NTC_TempGet();
                Bvd_Cal.Cur_Temp = (temperature_x100 + 50) / 100;  //获取NTC温度
                                                                   //					delay_us(60);

                WDT_Week();

                vi4302_read_ranging_data_with_firmware(ranging_data);  //获取16个字节的芯片数据
                VI4302_Temp_Bvd(&Bvd_Cal);

                confidence = Noise_To_Confidence((uint32_t)ranging_data[4] + ((uint32_t)ranging_data[5] << 8) + ((uint32_t)ranging_data[6] << 16),
                                                 *(uint16_t *)(ranging_data + 2));  //芯视界置信度计算
                cal_final_tof(ranging_data, temperature_x100, &MuL_TOF);

                if (MuL_TOF.TOF0 != 65535) {
                    if (70 <= confidence) {
                        yuanshi_tof_distance = MuL_TOF.TOF0;
                    } else  //置信度低
                    {
                        yuanshi_tof_distance = -30000;
                    }

                    if (10000 < yuanshi_tof_distance) {
                        yuanshi_tof_distance = -30000;
                    }
                } else {
                    yuanshi_tof_distance = -30000;
                    //				return;
                }

                VI4302_InfifoDataIn(yuanshi_tof_distance);

                g_ControlPara.vi4302_tof_data_no_tiaoling_offset = VI4302_InfifoDataOut();

                if (g_ControlPara.vi4302_tof_data_no_tiaoling_offset != -30000) {
                    g_ControlPara.vi4302_tof_data = g_ControlPara.vi4302_tof_data_no_tiaoling_offset + g_dataNeedSaved.point0_offset;
                } else {
                    g_ControlPara.vi4302_tof_data = g_ControlPara.vi4302_tof_data_no_tiaoling_offset;
                }


                //					if( gpio0_int_cnt > 10 ) //10个点打印一次
                {
                    *(int32_t *)(ranging_data + 16)  = (int32_t)temperature_x100;      //【4字节】
                    *(uint16_t *)(ranging_data + 20) = g_ControlPara.vi4302_tof_data;  //校准后的Tof【2字节】
                    ranging_data[22]                 = confidence;                     //置信度【单字节】

                    if (g_ControlPara.vi4302_ranging_state == 1) {
                        //					HEART_AutoSend(ranging_data, 23);  //自己的协议

                        UART_CmdAckSend(USER_RANGING_CMD, ranging_data, 23);  //芯视界协议
                    }

                    gpio0_int_cnt = 0;
                }
            }
        }
    }

    default:
        //			g_ControlPara.vi4302_tof_data = -30000;
        break;
    }
}


//系统复位
void SYSTERM_Init(void)  //系统复位
{
    //  __set_FAULTMASK(1);      //设置所有中断无效
    NVIC_SystemReset();  //设置系统软件复位
}

//累加和取反校验
uint8_t CMD_SumNegateCheck(uint8_t *pdata, uint8_t pdata_len) {
    uint8_t i       = 0;
    uint8_t sum_val = 0;

    for (i = 0; i < pdata_len; i++) {
        sum_val += pdata[i];
    }

    sum_val = (~sum_val);

    return sum_val;
}

//累加和校验
uint8_t CMD_SumCheck(uint8_t *pdata, uint8_t pdata_len) {
    uint8_t i       = 0;
    uint8_t sum_val = 0;

    for (i = 0; i < pdata_len; i++) {
        sum_val += pdata[i];
    }

    return sum_val;
}

void UART_CmdAckSend(uint8_t ack_cmd, uint8_t *pbuff, uint16_t pbuff_len) {
    uint8_t  uart_pbuff[64] = {0x5a, 0x00};
    uint16_t i = 1, j = 0;
    uart_pbuff[0]   = 0x5A;
    uart_pbuff[i++] = ack_cmd;
    uart_pbuff[i++] = (pbuff_len & 0x00ff);
    uart_pbuff[i++] = (pbuff_len >> 8);

    for (j = 0; j < pbuff_len; j++) {
        uart_pbuff[i++] = pbuff[j];
    }

    uart_pbuff[i++] = CMD_SumNegateCheck(&uart_pbuff[1], pbuff_len + 3);

    UART2_SendData(uart_pbuff, i);
}

//亿辰协议命令应答格式
void YC_UART_CmdAckSend(uint8_t cmd_val, uint8_t *pbuff, uint16_t pbuff_len) {
    uint8_t  pbuffer[UARTOUTFIFO_LEN] = {0x5A, 0x00};
    uint16_t i                        = 0;
    uint16_t j                        = 1;

    pbuffer[j++] = cmd_val | 0x80;
    pbuffer[j++] = pbuff_len;

    for (i = 0; i < pbuff_len; i++) {
        pbuffer[j++] = pbuff[i];
    }

    pbuffer[j] = CMD_SumCheck(pbuffer, j);

    UART2_SendData(pbuffer, (j + 1));
}

void HEART_AutoSend(uint8_t *pbuff, uint16_t pbuff_len) {
    YC_UART_CmdAckSend(VI430X_DATA_SEND_CMD, pbuff, pbuff_len);
}
