#include "main.h"
#include "ADC.h"
#include "GPIO.h"
#include "VI4302_System.h"
#include "VI4302_handle.h"
#include "cmd_handle.h"
#include "delay.h"
#include "n32g401.h"
#include "timer.h"
#include "usart.h"


void NVIC_SetVectorTable(uint32_t NVIC_VectTab, uint32_t Offset) {
    SCB->VTOR = NVIC_VectTab | Offset;
}

int main(void) {
#ifdef BOOT_APP
    NVIC_SetVectorTable(NVIC_VECTTAB_FLASH, 0x4000);
#endif

    //外部16M晶振,72M主频
    //  SetSysClockToPLL();

    NVIC_Priority_Group_Set(NVIC_PER2_SUB2_PRIORITYGROUP);

    WDT_Init();

    delay_ms(10);
    RCO_OutputInit();
    delay_ms(10);
    flash_DataInit();
    flash_SnDataInit();
    flash_FmDataInit();
    ALL_ParaInit();

    USART2_Init(460800);
    ADC_AllInit();

    GPIO_Init();

    if (VI4302_AllInit() == FIRMWARE_STATUS_ACK) {
        VI4302_Start_Ranging();
    }

    g_ControlPara.vi4302_ranging_state = 1;

    TIM6_Configuration();  // 1ms定时器

    while (1) {
        UART_AllHandle();
        Execute_instruction();  //执行指令
    }
}

// 1ms定时器
void TIM6_IRQHandler(void) {
    static uint16_t ms_count = 0;

    if (TIM_Interrupt_Status_Get(TIM6, TIM_INT_UPDATE) != RESET) {
        TIM_Interrupt_Status_Clear(TIM6, TIM_INT_UPDATE);

        ms_count++;
        if (10 <= ms_count) {
            ms_count = 0;
        }
    }
}

//外部中断
void EXTI2_IRQHandler(void) {
    if (RESET != EXTI_Interrupt_Status_Get(EXTI_LINE2)) {
        EXTI_Interrupt_Status_Clear(EXTI_LINE2);
        EXTI_FLAG++;
    }
}

//外部中断
void EXTI0_IRQHandler(void) {
    if (RESET != EXTI_Interrupt_Status_Get(EXTI_LINE0)) {
        EXTI_Interrupt_Status_Clear(EXTI_LINE0);

        gpio0_int_cnt++;                           //该标志位置位用来计数，表示多少次数据周期后执行一次动作
        g_ControlPara.vi4302_data_valid_flag = 1;  //该标志位置位，代表4302芯片数据准备好，可以通过SPI接口读取。
    }
}
