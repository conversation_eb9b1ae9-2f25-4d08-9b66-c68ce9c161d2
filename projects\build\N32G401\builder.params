{"name": "dTof_redLight", "target": "N32G401", "toolchain": "AC5", "toolchainLocation": "D:\\Programs\\keil_v5\\ARM\\ARMCC", "toolchainCfgFile": "c:\\Users\\<USER>\\.vscode\\extensions\\cl.eide-3.23.11\\res\\data\\models/arm.v5.model.json", "buildMode": "fast|multhread", "showRepathOnLog": true, "threadNum": 8, "rootDir": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects", "dumpPath": "build\\N32G401", "outDir": "build\\N32G401", "ram": 16384, "rom": 65536, "incDirs": ["inc", ".", "../firmware/CMSIS/device/startup", "../firmware/CMSIS/device", "../firmware/CMSIS/core", "../firmware/n32g401_std_periph_driver/inc", "../BSP", "../BSP/cmd_handle", "../BSP/flash", "../BSP/uartfifo", "../BSP/VI4302 API/BIN", "../BSP/VI4302 API/inc", "../BSP/app", "../BSP/work_mode", ".cmsis/include", "RTE/_N32G401"], "libDirs": [], "defines": ["N32G401", "USE_STDPERIPH_DRIVER"], "sourceList": ["../BSP/GPIO.c", "../BSP/USART.c", "../BSP/VI4302 API/BIN/fw_44_00_00_80_R00.c", "../BSP/VI4302 API/src/A2_Configurable.c", "../BSP/VI4302 API/src/User_Driver.c", "../BSP/VI4302 API/src/VI4302_Config.c", "../BSP/VI4302 API/src/VI4302_Handle.c", "../BSP/VI4302 API/src/VI4302_System.c", "../BSP/VI4302 API/src/data_handle.c", "../BSP/adc.c", "../BSP/cmd_handle/cmd_handle.c", "../BSP/delay.c", "../BSP/flash/flash.c", "../BSP/spi.c", "../BSP/timer.c", "../BSP/uartfifo/uartfifo.C", "../BSP/wdt.c", "../BSP/work_mode/work_mode.c", "../firmware/CMSIS/device/startup/startup_n32g401.s", "../firmware/CMSIS/device/system_n32g401.c", "../firmware/n32g401_std_periph_driver/src/misc.c", "../firmware/n32g401_std_periph_driver/src/n32g401_adc.c", "../firmware/n32g401_std_periph_driver/src/n32g401_beeper.c", "../firmware/n32g401_std_periph_driver/src/n32g401_comp.c", "../firmware/n32g401_std_periph_driver/src/n32g401_crc.c", "../firmware/n32g401_std_periph_driver/src/n32g401_dbg.c", "../firmware/n32g401_std_periph_driver/src/n32g401_dma.c", "../firmware/n32g401_std_periph_driver/src/n32g401_exti.c", "../firmware/n32g401_std_periph_driver/src/n32g401_flash.c", "../firmware/n32g401_std_periph_driver/src/n32g401_gpio.c", "../firmware/n32g401_std_periph_driver/src/n32g401_i2c.c", "../firmware/n32g401_std_periph_driver/src/n32g401_iwdg.c", "../firmware/n32g401_std_periph_driver/src/n32g401_lptim.c", "../firmware/n32g401_std_periph_driver/src/n32g401_pwr.c", "../firmware/n32g401_std_periph_driver/src/n32g401_rcc.c", "../firmware/n32g401_std_periph_driver/src/n32g401_rtc.c", "../firmware/n32g401_std_periph_driver/src/n32g401_spi.c", "../firmware/n32g401_std_periph_driver/src/n32g401_tim.c", "../firmware/n32g401_std_periph_driver/src/n32g401_usart.c", "../firmware/n32g401_std_periph_driver/src/n32g401_wwdg.c", "src/main.c", "src/n32g401_it.c"], "alwaysInBuildSources": [], "sourceParams": {}, "options": {"version": 4, "beforeBuildTasks": [], "afterBuildTasks": [{"name": "[Copy linker output for Keil User Commands]", "command": "mkdir ${KEIL_OUTPUT_DIR} & copy \"${OutDir}\\${ProjectName}.axf\" \"${KEIL_OUTPUT_DIR}\\${ProjectName}.axf\"", "disable": false, "abortAfterFailed": true}, {"name": "fromelf --bin --output=.\\bin\\RCC_ClockConfig.bin .\\Objects\\RCC_ClockConfig.axf", "command": "fromelf --bin --output=.\\bin\\RCC_ClockConfig.bin .\\Objects\\RCC_ClockConfig.axf", "disable": false, "abortAfterFailed": true}, {"name": "axf to elf", "command": "axf2elf -d \"${ToolchainRoot}\" -i \"${OutDir}/${ProjectName}.axf\" -o \"${OutDir}/${ProjectName}.elf\" > \"${OutDir}/axf2elf.log\""}], "global": {"use-microLIB": true, "output-debug-info": "enable", "microcontroller-cpu": "cortex-m4-sp", "microcontroller-fpu": "cortex-m4-sp", "microcontroller-float": "cortex-m4-sp", "$arch-extensions": "", "$clang-arch-extensions": "", "$armlink-arch-extensions": ""}, "c/cpp-compiler": {"optimization": "level-0", "one-elf-section-per-function": true, "c99-mode": true, "C_FLAGS": "--diag_suppress=1 --diag_suppress=1295", "CXX_FLAGS": "--diag_suppress=1 --diag_suppress=1295", "warnings": "all-warnings", "gnu-extensions": true}, "asm-compiler": {}, "linker": {"output-format": "elf", "xo-base": "", "ro-base": "0x00000000", "rw-base": "0x20000000", "link-scatter": ["f:/13_Ya<PERSON>-Laser-DTof2dMS/development/firmware/yapha_redLight_fw/projects/build/N32G401/dTof_redLight.sct"]}}, "env": {"KEIL_OUTPUT_DIR": "Objects", "workspaceFolder": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects", "workspaceFolderBasename": "projects", "OutDir": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects\\build\\N32G401", "OutDirRoot": "build", "OutDirBase": "build\\N32G401", "ProjectName": "dTof_redLight", "ConfigName": "N32G401", "ProjectRoot": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects", "ExecutableName": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects\\build\\N32G401\\dTof_redLight", "ChipPackDir": ".pack/Nations/N32G401_DFP.1.1.0", "ChipName": "N32G401K8Q7-2", "SYS_Platform": "win32", "SYS_DirSep": "\\", "SYS_DirSeparator": "\\", "SYS_PathSep": ";", "SYS_PathSeparator": ";", "SYS_EOL": "\r\n", "EIDE_BUILDER_DIR": "c:\\Users\\<USER>\\.vscode\\extensions\\cl.eide-3.23.11\\res\\tools\\win32\\unify_builder", "EIDE_BINARIES_VER": "12.1.1", "EIDE_MSYS": "C:\\Users\\<USER>\\.eide\\bin\\builder\\msys\\bin", "ToolchainRoot": "D:\\Programs\\keil_v5\\ARM\\ARMCC"}, "sysPaths": []}