[{"directory": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects", "file": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\BSP\\GPIO.c", "command": "\"D:\\Programs\\keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -Iinc -I. -I../firmware/CMSIS/device/startup -I../firmware/CMSIS/device -I../firmware/CMSIS/core -I../firmware/n32g401_std_periph_driver/inc -I../BSP -I../BSP/cmd_handle -I../BSP/flash -I../BSP/uartfifo -I\"../BSP/VI4302 API/BIN\" -I\"../BSP/VI4302 API/inc\" -I../BSP/app -I../BSP/work_mode -I.cmsis/include -IRTE/_N32G401 -D\"N32G401\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --gnu --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\N32G401\\.obj\\__\\BSP\\GPIO.o --no_depend_system_headers --depend .\\build\\N32G401\\.obj\\__\\BSP\\GPIO.d .\\..\\BSP\\GPIO.c"}, {"directory": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects", "file": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\BSP\\USART.c", "command": "\"D:\\Programs\\keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -Iinc -I. -I../firmware/CMSIS/device/startup -I../firmware/CMSIS/device -I../firmware/CMSIS/core -I../firmware/n32g401_std_periph_driver/inc -I../BSP -I../BSP/cmd_handle -I../BSP/flash -I../BSP/uartfifo -I\"../BSP/VI4302 API/BIN\" -I\"../BSP/VI4302 API/inc\" -I../BSP/app -I../BSP/work_mode -I.cmsis/include -IRTE/_N32G401 -D\"N32G401\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --gnu --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\N32G401\\.obj\\__\\BSP\\USART.o --no_depend_system_headers --depend .\\build\\N32G401\\.obj\\__\\BSP\\USART.d .\\..\\BSP\\USART.c"}, {"directory": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects", "file": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\BSP\\VI4302 API\\BIN\\fw_44_00_00_80_R00.c", "command": "\"D:\\Programs\\keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -Iinc -I. -I../firmware/CMSIS/device/startup -I../firmware/CMSIS/device -I../firmware/CMSIS/core -I../firmware/n32g401_std_periph_driver/inc -I../BSP -I../BSP/cmd_handle -I../BSP/flash -I../BSP/uartfifo -I\"../BSP/VI4302 API/BIN\" -I\"../BSP/VI4302 API/inc\" -I../BSP/app -I../BSP/work_mode -I.cmsis/include -IRTE/_N32G401 -D\"N32G401\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --gnu --diag_suppress=1 --diag_suppress=1295 -g -o \".\\build\\N32G401\\.obj\\__\\BSP\\VI4302 API\\BIN\\fw_44_00_00_80_R00.o\" --no_depend_system_headers --depend \".\\build\\N32G401\\.obj\\__\\BSP\\VI4302 API\\BIN\\fw_44_00_00_80_R00.d\" \".\\..\\BSP\\VI4302 API\\BIN\\fw_44_00_00_80_R00.c\""}, {"directory": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects", "file": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\BSP\\VI4302 API\\src\\A2_Configurable.c", "command": "\"D:\\Programs\\keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -Iinc -I. -I../firmware/CMSIS/device/startup -I../firmware/CMSIS/device -I../firmware/CMSIS/core -I../firmware/n32g401_std_periph_driver/inc -I../BSP -I../BSP/cmd_handle -I../BSP/flash -I../BSP/uartfifo -I\"../BSP/VI4302 API/BIN\" -I\"../BSP/VI4302 API/inc\" -I../BSP/app -I../BSP/work_mode -I.cmsis/include -IRTE/_N32G401 -D\"N32G401\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --gnu --diag_suppress=1 --diag_suppress=1295 -g -o \".\\build\\N32G401\\.obj\\__\\BSP\\VI4302 API\\src\\A2_Configurable.o\" --no_depend_system_headers --depend \".\\build\\N32G401\\.obj\\__\\BSP\\VI4302 API\\src\\A2_Configurable.d\" \".\\..\\BSP\\VI4302 API\\src\\A2_Configurable.c\""}, {"directory": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects", "file": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\BSP\\VI4302 API\\src\\User_Driver.c", "command": "\"D:\\Programs\\keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -Iinc -I. -I../firmware/CMSIS/device/startup -I../firmware/CMSIS/device -I../firmware/CMSIS/core -I../firmware/n32g401_std_periph_driver/inc -I../BSP -I../BSP/cmd_handle -I../BSP/flash -I../BSP/uartfifo -I\"../BSP/VI4302 API/BIN\" -I\"../BSP/VI4302 API/inc\" -I../BSP/app -I../BSP/work_mode -I.cmsis/include -IRTE/_N32G401 -D\"N32G401\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --gnu --diag_suppress=1 --diag_suppress=1295 -g -o \".\\build\\N32G401\\.obj\\__\\BSP\\VI4302 API\\src\\User_Driver.o\" --no_depend_system_headers --depend \".\\build\\N32G401\\.obj\\__\\BSP\\VI4302 API\\src\\User_Driver.d\" \".\\..\\BSP\\VI4302 API\\src\\User_Driver.c\""}, {"directory": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects", "file": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\BSP\\VI4302 API\\src\\VI4302_Config.c", "command": "\"D:\\Programs\\keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -Iinc -I. -I../firmware/CMSIS/device/startup -I../firmware/CMSIS/device -I../firmware/CMSIS/core -I../firmware/n32g401_std_periph_driver/inc -I../BSP -I../BSP/cmd_handle -I../BSP/flash -I../BSP/uartfifo -I\"../BSP/VI4302 API/BIN\" -I\"../BSP/VI4302 API/inc\" -I../BSP/app -I../BSP/work_mode -I.cmsis/include -IRTE/_N32G401 -D\"N32G401\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --gnu --diag_suppress=1 --diag_suppress=1295 -g -o \".\\build\\N32G401\\.obj\\__\\BSP\\VI4302 API\\src\\VI4302_Config.o\" --no_depend_system_headers --depend \".\\build\\N32G401\\.obj\\__\\BSP\\VI4302 API\\src\\VI4302_Config.d\" \".\\..\\BSP\\VI4302 API\\src\\VI4302_Config.c\""}, {"directory": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects", "file": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\BSP\\VI4302 API\\src\\VI4302_Handle.c", "command": "\"D:\\Programs\\keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -Iinc -I. -I../firmware/CMSIS/device/startup -I../firmware/CMSIS/device -I../firmware/CMSIS/core -I../firmware/n32g401_std_periph_driver/inc -I../BSP -I../BSP/cmd_handle -I../BSP/flash -I../BSP/uartfifo -I\"../BSP/VI4302 API/BIN\" -I\"../BSP/VI4302 API/inc\" -I../BSP/app -I../BSP/work_mode -I.cmsis/include -IRTE/_N32G401 -D\"N32G401\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --gnu --diag_suppress=1 --diag_suppress=1295 -g -o \".\\build\\N32G401\\.obj\\__\\BSP\\VI4302 API\\src\\VI4302_Handle.o\" --no_depend_system_headers --depend \".\\build\\N32G401\\.obj\\__\\BSP\\VI4302 API\\src\\VI4302_Handle.d\" \".\\..\\BSP\\VI4302 API\\src\\VI4302_Handle.c\""}, {"directory": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects", "file": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\BSP\\VI4302 API\\src\\VI4302_System.c", "command": "\"D:\\Programs\\keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -Iinc -I. -I../firmware/CMSIS/device/startup -I../firmware/CMSIS/device -I../firmware/CMSIS/core -I../firmware/n32g401_std_periph_driver/inc -I../BSP -I../BSP/cmd_handle -I../BSP/flash -I../BSP/uartfifo -I\"../BSP/VI4302 API/BIN\" -I\"../BSP/VI4302 API/inc\" -I../BSP/app -I../BSP/work_mode -I.cmsis/include -IRTE/_N32G401 -D\"N32G401\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --gnu --diag_suppress=1 --diag_suppress=1295 -g -o \".\\build\\N32G401\\.obj\\__\\BSP\\VI4302 API\\src\\VI4302_System.o\" --no_depend_system_headers --depend \".\\build\\N32G401\\.obj\\__\\BSP\\VI4302 API\\src\\VI4302_System.d\" \".\\..\\BSP\\VI4302 API\\src\\VI4302_System.c\""}, {"directory": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects", "file": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\BSP\\VI4302 API\\src\\data_handle.c", "command": "\"D:\\Programs\\keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -Iinc -I. -I../firmware/CMSIS/device/startup -I../firmware/CMSIS/device -I../firmware/CMSIS/core -I../firmware/n32g401_std_periph_driver/inc -I../BSP -I../BSP/cmd_handle -I../BSP/flash -I../BSP/uartfifo -I\"../BSP/VI4302 API/BIN\" -I\"../BSP/VI4302 API/inc\" -I../BSP/app -I../BSP/work_mode -I.cmsis/include -IRTE/_N32G401 -D\"N32G401\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --gnu --diag_suppress=1 --diag_suppress=1295 -g -o \".\\build\\N32G401\\.obj\\__\\BSP\\VI4302 API\\src\\data_handle.o\" --no_depend_system_headers --depend \".\\build\\N32G401\\.obj\\__\\BSP\\VI4302 API\\src\\data_handle.d\" \".\\..\\BSP\\VI4302 API\\src\\data_handle.c\""}, {"directory": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects", "file": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\BSP\\adc.c", "command": "\"D:\\Programs\\keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -Iinc -I. -I../firmware/CMSIS/device/startup -I../firmware/CMSIS/device -I../firmware/CMSIS/core -I../firmware/n32g401_std_periph_driver/inc -I../BSP -I../BSP/cmd_handle -I../BSP/flash -I../BSP/uartfifo -I\"../BSP/VI4302 API/BIN\" -I\"../BSP/VI4302 API/inc\" -I../BSP/app -I../BSP/work_mode -I.cmsis/include -IRTE/_N32G401 -D\"N32G401\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --gnu --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\N32G401\\.obj\\__\\BSP\\adc.o --no_depend_system_headers --depend .\\build\\N32G401\\.obj\\__\\BSP\\adc.d .\\..\\BSP\\adc.c"}, {"directory": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects", "file": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\BSP\\cmd_handle\\cmd_handle.c", "command": "\"D:\\Programs\\keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -Iinc -I. -I../firmware/CMSIS/device/startup -I../firmware/CMSIS/device -I../firmware/CMSIS/core -I../firmware/n32g401_std_periph_driver/inc -I../BSP -I../BSP/cmd_handle -I../BSP/flash -I../BSP/uartfifo -I\"../BSP/VI4302 API/BIN\" -I\"../BSP/VI4302 API/inc\" -I../BSP/app -I../BSP/work_mode -I.cmsis/include -IRTE/_N32G401 -D\"N32G401\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --gnu --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\N32G401\\.obj\\__\\BSP\\cmd_handle\\cmd_handle.o --no_depend_system_headers --depend .\\build\\N32G401\\.obj\\__\\BSP\\cmd_handle\\cmd_handle.d .\\..\\BSP\\cmd_handle\\cmd_handle.c"}, {"directory": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects", "file": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\BSP\\delay.c", "command": "\"D:\\Programs\\keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -Iinc -I. -I../firmware/CMSIS/device/startup -I../firmware/CMSIS/device -I../firmware/CMSIS/core -I../firmware/n32g401_std_periph_driver/inc -I../BSP -I../BSP/cmd_handle -I../BSP/flash -I../BSP/uartfifo -I\"../BSP/VI4302 API/BIN\" -I\"../BSP/VI4302 API/inc\" -I../BSP/app -I../BSP/work_mode -I.cmsis/include -IRTE/_N32G401 -D\"N32G401\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --gnu --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\N32G401\\.obj\\__\\BSP\\delay.o --no_depend_system_headers --depend .\\build\\N32G401\\.obj\\__\\BSP\\delay.d .\\..\\BSP\\delay.c"}, {"directory": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects", "file": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\BSP\\flash\\flash.c", "command": "\"D:\\Programs\\keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -Iinc -I. -I../firmware/CMSIS/device/startup -I../firmware/CMSIS/device -I../firmware/CMSIS/core -I../firmware/n32g401_std_periph_driver/inc -I../BSP -I../BSP/cmd_handle -I../BSP/flash -I../BSP/uartfifo -I\"../BSP/VI4302 API/BIN\" -I\"../BSP/VI4302 API/inc\" -I../BSP/app -I../BSP/work_mode -I.cmsis/include -IRTE/_N32G401 -D\"N32G401\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --gnu --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\N32G401\\.obj\\__\\BSP\\flash\\flash.o --no_depend_system_headers --depend .\\build\\N32G401\\.obj\\__\\BSP\\flash\\flash.d .\\..\\BSP\\flash\\flash.c"}, {"directory": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects", "file": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\BSP\\spi.c", "command": "\"D:\\Programs\\keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -Iinc -I. -I../firmware/CMSIS/device/startup -I../firmware/CMSIS/device -I../firmware/CMSIS/core -I../firmware/n32g401_std_periph_driver/inc -I../BSP -I../BSP/cmd_handle -I../BSP/flash -I../BSP/uartfifo -I\"../BSP/VI4302 API/BIN\" -I\"../BSP/VI4302 API/inc\" -I../BSP/app -I../BSP/work_mode -I.cmsis/include -IRTE/_N32G401 -D\"N32G401\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --gnu --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\N32G401\\.obj\\__\\BSP\\spi.o --no_depend_system_headers --depend .\\build\\N32G401\\.obj\\__\\BSP\\spi.d .\\..\\BSP\\spi.c"}, {"directory": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects", "file": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\BSP\\timer.c", "command": "\"D:\\Programs\\keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -Iinc -I. -I../firmware/CMSIS/device/startup -I../firmware/CMSIS/device -I../firmware/CMSIS/core -I../firmware/n32g401_std_periph_driver/inc -I../BSP -I../BSP/cmd_handle -I../BSP/flash -I../BSP/uartfifo -I\"../BSP/VI4302 API/BIN\" -I\"../BSP/VI4302 API/inc\" -I../BSP/app -I../BSP/work_mode -I.cmsis/include -IRTE/_N32G401 -D\"N32G401\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --gnu --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\N32G401\\.obj\\__\\BSP\\timer.o --no_depend_system_headers --depend .\\build\\N32G401\\.obj\\__\\BSP\\timer.d .\\..\\BSP\\timer.c"}, {"directory": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects", "file": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\BSP\\uartfifo\\uartfifo.C", "command": "\"D:\\Programs\\keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -Iinc -I. -I../firmware/CMSIS/device/startup -I../firmware/CMSIS/device -I../firmware/CMSIS/core -I../firmware/n32g401_std_periph_driver/inc -I../BSP -I../BSP/cmd_handle -I../BSP/flash -I../BSP/uartfifo -I\"../BSP/VI4302 API/BIN\" -I\"../BSP/VI4302 API/inc\" -I../BSP/app -I../BSP/work_mode -I.cmsis/include -IRTE/_N32G401 -D\"N32G401\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --gnu --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\N32G401\\.obj\\__\\BSP\\uartfifo\\uartfifo.o --no_depend_system_headers --depend .\\build\\N32G401\\.obj\\__\\BSP\\uartfifo\\uartfifo.d .\\..\\BSP\\uartfifo\\uartfifo.C"}, {"directory": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects", "file": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\BSP\\wdt.c", "command": "\"D:\\Programs\\keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -Iinc -I. -I../firmware/CMSIS/device/startup -I../firmware/CMSIS/device -I../firmware/CMSIS/core -I../firmware/n32g401_std_periph_driver/inc -I../BSP -I../BSP/cmd_handle -I../BSP/flash -I../BSP/uartfifo -I\"../BSP/VI4302 API/BIN\" -I\"../BSP/VI4302 API/inc\" -I../BSP/app -I../BSP/work_mode -I.cmsis/include -IRTE/_N32G401 -D\"N32G401\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --gnu --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\N32G401\\.obj\\__\\BSP\\wdt.o --no_depend_system_headers --depend .\\build\\N32G401\\.obj\\__\\BSP\\wdt.d .\\..\\BSP\\wdt.c"}, {"directory": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects", "file": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\BSP\\work_mode\\work_mode.c", "command": "\"D:\\Programs\\keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -Iinc -I. -I../firmware/CMSIS/device/startup -I../firmware/CMSIS/device -I../firmware/CMSIS/core -I../firmware/n32g401_std_periph_driver/inc -I../BSP -I../BSP/cmd_handle -I../BSP/flash -I../BSP/uartfifo -I\"../BSP/VI4302 API/BIN\" -I\"../BSP/VI4302 API/inc\" -I../BSP/app -I../BSP/work_mode -I.cmsis/include -IRTE/_N32G401 -D\"N32G401\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --gnu --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\N32G401\\.obj\\__\\BSP\\work_mode\\work_mode.o --no_depend_system_headers --depend .\\build\\N32G401\\.obj\\__\\BSP\\work_mode\\work_mode.d .\\..\\BSP\\work_mode\\work_mode.c"}, {"directory": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects", "file": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\firmware\\CMSIS\\device\\startup\\startup_n32g401.s", "command": "\"D:\\Programs\\keil_v5\\ARM\\ARMCC\\bin\\armasm.exe\" --apcs=interwork -Iinc -I. -I../firmware/CMSIS/device/startup -I../firmware/CMSIS/device -I../firmware/CMSIS/core -I../firmware/n32g401_std_periph_driver/inc -I../BSP -I../BSP/cmd_handle -I../BSP/flash -I../BSP/uartfifo -I\"../BSP/VI4302 API/BIN\" -I\"../BSP/VI4302 API/inc\" -I../BSP/app -I../BSP/work_mode -I.cmsis/include -IRTE/_N32G401 --cpu Cortex-M4.fp --li --pd \"__MICROLIB SETA 1\" -g -o .\\build\\N32G401\\.obj\\__\\firmware\\CMSIS\\device\\startup\\startup_n32g401.o --depend .\\build\\N32G401\\.obj\\__\\firmware\\CMSIS\\device\\startup\\startup_n32g401.d .\\..\\firmware\\CMSIS\\device\\startup\\startup_n32g401.s"}, {"directory": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects", "file": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\firmware\\CMSIS\\device\\system_n32g401.c", "command": "\"D:\\Programs\\keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -Iinc -I. -I../firmware/CMSIS/device/startup -I../firmware/CMSIS/device -I../firmware/CMSIS/core -I../firmware/n32g401_std_periph_driver/inc -I../BSP -I../BSP/cmd_handle -I../BSP/flash -I../BSP/uartfifo -I\"../BSP/VI4302 API/BIN\" -I\"../BSP/VI4302 API/inc\" -I../BSP/app -I../BSP/work_mode -I.cmsis/include -IRTE/_N32G401 -D\"N32G401\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --gnu --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\N32G401\\.obj\\__\\firmware\\CMSIS\\device\\system_n32g401.o --no_depend_system_headers --depend .\\build\\N32G401\\.obj\\__\\firmware\\CMSIS\\device\\system_n32g401.d .\\..\\firmware\\CMSIS\\device\\system_n32g401.c"}, {"directory": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects", "file": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\firmware\\n32g401_std_periph_driver\\src\\misc.c", "command": "\"D:\\Programs\\keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -Iinc -I. -I../firmware/CMSIS/device/startup -I../firmware/CMSIS/device -I../firmware/CMSIS/core -I../firmware/n32g401_std_periph_driver/inc -I../BSP -I../BSP/cmd_handle -I../BSP/flash -I../BSP/uartfifo -I\"../BSP/VI4302 API/BIN\" -I\"../BSP/VI4302 API/inc\" -I../BSP/app -I../BSP/work_mode -I.cmsis/include -IRTE/_N32G401 -D\"N32G401\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --gnu --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\N32G401\\.obj\\__\\firmware\\n32g401_std_periph_driver\\src\\misc.o --no_depend_system_headers --depend .\\build\\N32G401\\.obj\\__\\firmware\\n32g401_std_periph_driver\\src\\misc.d .\\..\\firmware\\n32g401_std_periph_driver\\src\\misc.c"}, {"directory": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects", "file": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\firmware\\n32g401_std_periph_driver\\src\\n32g401_adc.c", "command": "\"D:\\Programs\\keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -Iinc -I. -I../firmware/CMSIS/device/startup -I../firmware/CMSIS/device -I../firmware/CMSIS/core -I../firmware/n32g401_std_periph_driver/inc -I../BSP -I../BSP/cmd_handle -I../BSP/flash -I../BSP/uartfifo -I\"../BSP/VI4302 API/BIN\" -I\"../BSP/VI4302 API/inc\" -I../BSP/app -I../BSP/work_mode -I.cmsis/include -IRTE/_N32G401 -D\"N32G401\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --gnu --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\N32G401\\.obj\\__\\firmware\\n32g401_std_periph_driver\\src\\n32g401_adc.o --no_depend_system_headers --depend .\\build\\N32G401\\.obj\\__\\firmware\\n32g401_std_periph_driver\\src\\n32g401_adc.d .\\..\\firmware\\n32g401_std_periph_driver\\src\\n32g401_adc.c"}, {"directory": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects", "file": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\firmware\\n32g401_std_periph_driver\\src\\n32g401_beeper.c", "command": "\"D:\\Programs\\keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -Iinc -I. -I../firmware/CMSIS/device/startup -I../firmware/CMSIS/device -I../firmware/CMSIS/core -I../firmware/n32g401_std_periph_driver/inc -I../BSP -I../BSP/cmd_handle -I../BSP/flash -I../BSP/uartfifo -I\"../BSP/VI4302 API/BIN\" -I\"../BSP/VI4302 API/inc\" -I../BSP/app -I../BSP/work_mode -I.cmsis/include -IRTE/_N32G401 -D\"N32G401\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --gnu --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\N32G401\\.obj\\__\\firmware\\n32g401_std_periph_driver\\src\\n32g401_beeper.o --no_depend_system_headers --depend .\\build\\N32G401\\.obj\\__\\firmware\\n32g401_std_periph_driver\\src\\n32g401_beeper.d .\\..\\firmware\\n32g401_std_periph_driver\\src\\n32g401_beeper.c"}, {"directory": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects", "file": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\firmware\\n32g401_std_periph_driver\\src\\n32g401_comp.c", "command": "\"D:\\Programs\\keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -Iinc -I. -I../firmware/CMSIS/device/startup -I../firmware/CMSIS/device -I../firmware/CMSIS/core -I../firmware/n32g401_std_periph_driver/inc -I../BSP -I../BSP/cmd_handle -I../BSP/flash -I../BSP/uartfifo -I\"../BSP/VI4302 API/BIN\" -I\"../BSP/VI4302 API/inc\" -I../BSP/app -I../BSP/work_mode -I.cmsis/include -IRTE/_N32G401 -D\"N32G401\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --gnu --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\N32G401\\.obj\\__\\firmware\\n32g401_std_periph_driver\\src\\n32g401_comp.o --no_depend_system_headers --depend .\\build\\N32G401\\.obj\\__\\firmware\\n32g401_std_periph_driver\\src\\n32g401_comp.d .\\..\\firmware\\n32g401_std_periph_driver\\src\\n32g401_comp.c"}, {"directory": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects", "file": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\firmware\\n32g401_std_periph_driver\\src\\n32g401_crc.c", "command": "\"D:\\Programs\\keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -Iinc -I. -I../firmware/CMSIS/device/startup -I../firmware/CMSIS/device -I../firmware/CMSIS/core -I../firmware/n32g401_std_periph_driver/inc -I../BSP -I../BSP/cmd_handle -I../BSP/flash -I../BSP/uartfifo -I\"../BSP/VI4302 API/BIN\" -I\"../BSP/VI4302 API/inc\" -I../BSP/app -I../BSP/work_mode -I.cmsis/include -IRTE/_N32G401 -D\"N32G401\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --gnu --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\N32G401\\.obj\\__\\firmware\\n32g401_std_periph_driver\\src\\n32g401_crc.o --no_depend_system_headers --depend .\\build\\N32G401\\.obj\\__\\firmware\\n32g401_std_periph_driver\\src\\n32g401_crc.d .\\..\\firmware\\n32g401_std_periph_driver\\src\\n32g401_crc.c"}, {"directory": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects", "file": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\firmware\\n32g401_std_periph_driver\\src\\n32g401_dbg.c", "command": "\"D:\\Programs\\keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -Iinc -I. -I../firmware/CMSIS/device/startup -I../firmware/CMSIS/device -I../firmware/CMSIS/core -I../firmware/n32g401_std_periph_driver/inc -I../BSP -I../BSP/cmd_handle -I../BSP/flash -I../BSP/uartfifo -I\"../BSP/VI4302 API/BIN\" -I\"../BSP/VI4302 API/inc\" -I../BSP/app -I../BSP/work_mode -I.cmsis/include -IRTE/_N32G401 -D\"N32G401\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --gnu --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\N32G401\\.obj\\__\\firmware\\n32g401_std_periph_driver\\src\\n32g401_dbg.o --no_depend_system_headers --depend .\\build\\N32G401\\.obj\\__\\firmware\\n32g401_std_periph_driver\\src\\n32g401_dbg.d .\\..\\firmware\\n32g401_std_periph_driver\\src\\n32g401_dbg.c"}, {"directory": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects", "file": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\firmware\\n32g401_std_periph_driver\\src\\n32g401_dma.c", "command": "\"D:\\Programs\\keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -Iinc -I. -I../firmware/CMSIS/device/startup -I../firmware/CMSIS/device -I../firmware/CMSIS/core -I../firmware/n32g401_std_periph_driver/inc -I../BSP -I../BSP/cmd_handle -I../BSP/flash -I../BSP/uartfifo -I\"../BSP/VI4302 API/BIN\" -I\"../BSP/VI4302 API/inc\" -I../BSP/app -I../BSP/work_mode -I.cmsis/include -IRTE/_N32G401 -D\"N32G401\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --gnu --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\N32G401\\.obj\\__\\firmware\\n32g401_std_periph_driver\\src\\n32g401_dma.o --no_depend_system_headers --depend .\\build\\N32G401\\.obj\\__\\firmware\\n32g401_std_periph_driver\\src\\n32g401_dma.d .\\..\\firmware\\n32g401_std_periph_driver\\src\\n32g401_dma.c"}, {"directory": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects", "file": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\firmware\\n32g401_std_periph_driver\\src\\n32g401_exti.c", "command": "\"D:\\Programs\\keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -Iinc -I. -I../firmware/CMSIS/device/startup -I../firmware/CMSIS/device -I../firmware/CMSIS/core -I../firmware/n32g401_std_periph_driver/inc -I../BSP -I../BSP/cmd_handle -I../BSP/flash -I../BSP/uartfifo -I\"../BSP/VI4302 API/BIN\" -I\"../BSP/VI4302 API/inc\" -I../BSP/app -I../BSP/work_mode -I.cmsis/include -IRTE/_N32G401 -D\"N32G401\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --gnu --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\N32G401\\.obj\\__\\firmware\\n32g401_std_periph_driver\\src\\n32g401_exti.o --no_depend_system_headers --depend .\\build\\N32G401\\.obj\\__\\firmware\\n32g401_std_periph_driver\\src\\n32g401_exti.d .\\..\\firmware\\n32g401_std_periph_driver\\src\\n32g401_exti.c"}, {"directory": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects", "file": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\firmware\\n32g401_std_periph_driver\\src\\n32g401_flash.c", "command": "\"D:\\Programs\\keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -Iinc -I. -I../firmware/CMSIS/device/startup -I../firmware/CMSIS/device -I../firmware/CMSIS/core -I../firmware/n32g401_std_periph_driver/inc -I../BSP -I../BSP/cmd_handle -I../BSP/flash -I../BSP/uartfifo -I\"../BSP/VI4302 API/BIN\" -I\"../BSP/VI4302 API/inc\" -I../BSP/app -I../BSP/work_mode -I.cmsis/include -IRTE/_N32G401 -D\"N32G401\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --gnu --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\N32G401\\.obj\\__\\firmware\\n32g401_std_periph_driver\\src\\n32g401_flash.o --no_depend_system_headers --depend .\\build\\N32G401\\.obj\\__\\firmware\\n32g401_std_periph_driver\\src\\n32g401_flash.d .\\..\\firmware\\n32g401_std_periph_driver\\src\\n32g401_flash.c"}, {"directory": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects", "file": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\firmware\\n32g401_std_periph_driver\\src\\n32g401_gpio.c", "command": "\"D:\\Programs\\keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -Iinc -I. -I../firmware/CMSIS/device/startup -I../firmware/CMSIS/device -I../firmware/CMSIS/core -I../firmware/n32g401_std_periph_driver/inc -I../BSP -I../BSP/cmd_handle -I../BSP/flash -I../BSP/uartfifo -I\"../BSP/VI4302 API/BIN\" -I\"../BSP/VI4302 API/inc\" -I../BSP/app -I../BSP/work_mode -I.cmsis/include -IRTE/_N32G401 -D\"N32G401\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --gnu --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\N32G401\\.obj\\__\\firmware\\n32g401_std_periph_driver\\src\\n32g401_gpio.o --no_depend_system_headers --depend .\\build\\N32G401\\.obj\\__\\firmware\\n32g401_std_periph_driver\\src\\n32g401_gpio.d .\\..\\firmware\\n32g401_std_periph_driver\\src\\n32g401_gpio.c"}, {"directory": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects", "file": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\firmware\\n32g401_std_periph_driver\\src\\n32g401_i2c.c", "command": "\"D:\\Programs\\keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -Iinc -I. -I../firmware/CMSIS/device/startup -I../firmware/CMSIS/device -I../firmware/CMSIS/core -I../firmware/n32g401_std_periph_driver/inc -I../BSP -I../BSP/cmd_handle -I../BSP/flash -I../BSP/uartfifo -I\"../BSP/VI4302 API/BIN\" -I\"../BSP/VI4302 API/inc\" -I../BSP/app -I../BSP/work_mode -I.cmsis/include -IRTE/_N32G401 -D\"N32G401\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --gnu --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\N32G401\\.obj\\__\\firmware\\n32g401_std_periph_driver\\src\\n32g401_i2c.o --no_depend_system_headers --depend .\\build\\N32G401\\.obj\\__\\firmware\\n32g401_std_periph_driver\\src\\n32g401_i2c.d .\\..\\firmware\\n32g401_std_periph_driver\\src\\n32g401_i2c.c"}, {"directory": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects", "file": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\firmware\\n32g401_std_periph_driver\\src\\n32g401_iwdg.c", "command": "\"D:\\Programs\\keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -Iinc -I. -I../firmware/CMSIS/device/startup -I../firmware/CMSIS/device -I../firmware/CMSIS/core -I../firmware/n32g401_std_periph_driver/inc -I../BSP -I../BSP/cmd_handle -I../BSP/flash -I../BSP/uartfifo -I\"../BSP/VI4302 API/BIN\" -I\"../BSP/VI4302 API/inc\" -I../BSP/app -I../BSP/work_mode -I.cmsis/include -IRTE/_N32G401 -D\"N32G401\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --gnu --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\N32G401\\.obj\\__\\firmware\\n32g401_std_periph_driver\\src\\n32g401_iwdg.o --no_depend_system_headers --depend .\\build\\N32G401\\.obj\\__\\firmware\\n32g401_std_periph_driver\\src\\n32g401_iwdg.d .\\..\\firmware\\n32g401_std_periph_driver\\src\\n32g401_iwdg.c"}, {"directory": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects", "file": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\firmware\\n32g401_std_periph_driver\\src\\n32g401_lptim.c", "command": "\"D:\\Programs\\keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -Iinc -I. -I../firmware/CMSIS/device/startup -I../firmware/CMSIS/device -I../firmware/CMSIS/core -I../firmware/n32g401_std_periph_driver/inc -I../BSP -I../BSP/cmd_handle -I../BSP/flash -I../BSP/uartfifo -I\"../BSP/VI4302 API/BIN\" -I\"../BSP/VI4302 API/inc\" -I../BSP/app -I../BSP/work_mode -I.cmsis/include -IRTE/_N32G401 -D\"N32G401\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --gnu --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\N32G401\\.obj\\__\\firmware\\n32g401_std_periph_driver\\src\\n32g401_lptim.o --no_depend_system_headers --depend .\\build\\N32G401\\.obj\\__\\firmware\\n32g401_std_periph_driver\\src\\n32g401_lptim.d .\\..\\firmware\\n32g401_std_periph_driver\\src\\n32g401_lptim.c"}, {"directory": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects", "file": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\firmware\\n32g401_std_periph_driver\\src\\n32g401_pwr.c", "command": "\"D:\\Programs\\keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -Iinc -I. -I../firmware/CMSIS/device/startup -I../firmware/CMSIS/device -I../firmware/CMSIS/core -I../firmware/n32g401_std_periph_driver/inc -I../BSP -I../BSP/cmd_handle -I../BSP/flash -I../BSP/uartfifo -I\"../BSP/VI4302 API/BIN\" -I\"../BSP/VI4302 API/inc\" -I../BSP/app -I../BSP/work_mode -I.cmsis/include -IRTE/_N32G401 -D\"N32G401\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --gnu --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\N32G401\\.obj\\__\\firmware\\n32g401_std_periph_driver\\src\\n32g401_pwr.o --no_depend_system_headers --depend .\\build\\N32G401\\.obj\\__\\firmware\\n32g401_std_periph_driver\\src\\n32g401_pwr.d .\\..\\firmware\\n32g401_std_periph_driver\\src\\n32g401_pwr.c"}, {"directory": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects", "file": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\firmware\\n32g401_std_periph_driver\\src\\n32g401_rcc.c", "command": "\"D:\\Programs\\keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -Iinc -I. -I../firmware/CMSIS/device/startup -I../firmware/CMSIS/device -I../firmware/CMSIS/core -I../firmware/n32g401_std_periph_driver/inc -I../BSP -I../BSP/cmd_handle -I../BSP/flash -I../BSP/uartfifo -I\"../BSP/VI4302 API/BIN\" -I\"../BSP/VI4302 API/inc\" -I../BSP/app -I../BSP/work_mode -I.cmsis/include -IRTE/_N32G401 -D\"N32G401\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --gnu --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\N32G401\\.obj\\__\\firmware\\n32g401_std_periph_driver\\src\\n32g401_rcc.o --no_depend_system_headers --depend .\\build\\N32G401\\.obj\\__\\firmware\\n32g401_std_periph_driver\\src\\n32g401_rcc.d .\\..\\firmware\\n32g401_std_periph_driver\\src\\n32g401_rcc.c"}, {"directory": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects", "file": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\firmware\\n32g401_std_periph_driver\\src\\n32g401_rtc.c", "command": "\"D:\\Programs\\keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -Iinc -I. -I../firmware/CMSIS/device/startup -I../firmware/CMSIS/device -I../firmware/CMSIS/core -I../firmware/n32g401_std_periph_driver/inc -I../BSP -I../BSP/cmd_handle -I../BSP/flash -I../BSP/uartfifo -I\"../BSP/VI4302 API/BIN\" -I\"../BSP/VI4302 API/inc\" -I../BSP/app -I../BSP/work_mode -I.cmsis/include -IRTE/_N32G401 -D\"N32G401\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --gnu --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\N32G401\\.obj\\__\\firmware\\n32g401_std_periph_driver\\src\\n32g401_rtc.o --no_depend_system_headers --depend .\\build\\N32G401\\.obj\\__\\firmware\\n32g401_std_periph_driver\\src\\n32g401_rtc.d .\\..\\firmware\\n32g401_std_periph_driver\\src\\n32g401_rtc.c"}, {"directory": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects", "file": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\firmware\\n32g401_std_periph_driver\\src\\n32g401_spi.c", "command": "\"D:\\Programs\\keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -Iinc -I. -I../firmware/CMSIS/device/startup -I../firmware/CMSIS/device -I../firmware/CMSIS/core -I../firmware/n32g401_std_periph_driver/inc -I../BSP -I../BSP/cmd_handle -I../BSP/flash -I../BSP/uartfifo -I\"../BSP/VI4302 API/BIN\" -I\"../BSP/VI4302 API/inc\" -I../BSP/app -I../BSP/work_mode -I.cmsis/include -IRTE/_N32G401 -D\"N32G401\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --gnu --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\N32G401\\.obj\\__\\firmware\\n32g401_std_periph_driver\\src\\n32g401_spi.o --no_depend_system_headers --depend .\\build\\N32G401\\.obj\\__\\firmware\\n32g401_std_periph_driver\\src\\n32g401_spi.d .\\..\\firmware\\n32g401_std_periph_driver\\src\\n32g401_spi.c"}, {"directory": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects", "file": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\firmware\\n32g401_std_periph_driver\\src\\n32g401_tim.c", "command": "\"D:\\Programs\\keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -Iinc -I. -I../firmware/CMSIS/device/startup -I../firmware/CMSIS/device -I../firmware/CMSIS/core -I../firmware/n32g401_std_periph_driver/inc -I../BSP -I../BSP/cmd_handle -I../BSP/flash -I../BSP/uartfifo -I\"../BSP/VI4302 API/BIN\" -I\"../BSP/VI4302 API/inc\" -I../BSP/app -I../BSP/work_mode -I.cmsis/include -IRTE/_N32G401 -D\"N32G401\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --gnu --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\N32G401\\.obj\\__\\firmware\\n32g401_std_periph_driver\\src\\n32g401_tim.o --no_depend_system_headers --depend .\\build\\N32G401\\.obj\\__\\firmware\\n32g401_std_periph_driver\\src\\n32g401_tim.d .\\..\\firmware\\n32g401_std_periph_driver\\src\\n32g401_tim.c"}, {"directory": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects", "file": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\firmware\\n32g401_std_periph_driver\\src\\n32g401_usart.c", "command": "\"D:\\Programs\\keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -Iinc -I. -I../firmware/CMSIS/device/startup -I../firmware/CMSIS/device -I../firmware/CMSIS/core -I../firmware/n32g401_std_periph_driver/inc -I../BSP -I../BSP/cmd_handle -I../BSP/flash -I../BSP/uartfifo -I\"../BSP/VI4302 API/BIN\" -I\"../BSP/VI4302 API/inc\" -I../BSP/app -I../BSP/work_mode -I.cmsis/include -IRTE/_N32G401 -D\"N32G401\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --gnu --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\N32G401\\.obj\\__\\firmware\\n32g401_std_periph_driver\\src\\n32g401_usart.o --no_depend_system_headers --depend .\\build\\N32G401\\.obj\\__\\firmware\\n32g401_std_periph_driver\\src\\n32g401_usart.d .\\..\\firmware\\n32g401_std_periph_driver\\src\\n32g401_usart.c"}, {"directory": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects", "file": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\firmware\\n32g401_std_periph_driver\\src\\n32g401_wwdg.c", "command": "\"D:\\Programs\\keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -Iinc -I. -I../firmware/CMSIS/device/startup -I../firmware/CMSIS/device -I../firmware/CMSIS/core -I../firmware/n32g401_std_periph_driver/inc -I../BSP -I../BSP/cmd_handle -I../BSP/flash -I../BSP/uartfifo -I\"../BSP/VI4302 API/BIN\" -I\"../BSP/VI4302 API/inc\" -I../BSP/app -I../BSP/work_mode -I.cmsis/include -IRTE/_N32G401 -D\"N32G401\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --gnu --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\N32G401\\.obj\\__\\firmware\\n32g401_std_periph_driver\\src\\n32g401_wwdg.o --no_depend_system_headers --depend .\\build\\N32G401\\.obj\\__\\firmware\\n32g401_std_periph_driver\\src\\n32g401_wwdg.d .\\..\\firmware\\n32g401_std_periph_driver\\src\\n32g401_wwdg.c"}, {"directory": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects", "file": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects\\src\\main.c", "command": "\"D:\\Programs\\keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -Iinc -I. -I../firmware/CMSIS/device/startup -I../firmware/CMSIS/device -I../firmware/CMSIS/core -I../firmware/n32g401_std_periph_driver/inc -I../BSP -I../BSP/cmd_handle -I../BSP/flash -I../BSP/uartfifo -I\"../BSP/VI4302 API/BIN\" -I\"../BSP/VI4302 API/inc\" -I../BSP/app -I../BSP/work_mode -I.cmsis/include -IRTE/_N32G401 -D\"N32G401\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --gnu --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\N32G401\\.obj\\src\\main.o --no_depend_system_headers --depend .\\build\\N32G401\\.obj\\src\\main.d .\\src\\main.c"}, {"directory": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects", "file": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects\\src\\n32g401_it.c", "command": "\"D:\\Programs\\keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -Iinc -I. -I../firmware/CMSIS/device/startup -I../firmware/CMSIS/device -I../firmware/CMSIS/core -I../firmware/n32g401_std_periph_driver/inc -I../BSP -I../BSP/cmd_handle -I../BSP/flash -I../BSP/uartfifo -I\"../BSP/VI4302 API/BIN\" -I\"../BSP/VI4302 API/inc\" -I../BSP/app -I../BSP/work_mode -I.cmsis/include -IRTE/_N32G401 -D\"N32G401\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --gnu --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\N32G401\\.obj\\src\\n32g401_it.o --no_depend_system_headers --depend .\\build\\N32G401\\.obj\\src\\n32g401_it.d .\\src\\n32g401_it.c"}]