{"name": "dTof_redLight", "type": "ARM", "dependenceList": [], "srcDirs": [], "virtualFolder": {"name": "<virtual_root>", "files": [], "folders": [{"name": "USER", "files": [{"path": "src/main.c"}, {"path": "src/n32g401_it.c"}], "folders": []}, {"name": "BSP", "files": [{"path": "../BSP/GPIO.c"}, {"path": "../BSP/USART.c"}, {"path": "../BSP/flash/flash.c"}, {"path": "../BSP/uartfifo/uartfifo.C"}, {"path": "../BSP/adc.c"}, {"path": "../BSP/cmd_handle/cmd_handle.c"}, {"path": "../BSP/delay.c"}, {"path": "../BSP/spi.c"}, {"path": "../BSP/timer.c"}, {"path": "../BSP/wdt.c"}, {"path": "../BSP/app/converter.h"}, {"path": "../BSP/work_mode/work_mode.c"}], "folders": []}, {"name": "VI4302", "files": [{"path": "../BSP/VI4302 API/src/data_handle.c"}, {"path": "../BSP/VI4302 API/src/User_Driver.c"}, {"path": "../BSP/VI4302 API/src/VI4302_Config.c"}, {"path": "../BSP/VI4302 API/src/VI4302_Handle.c"}, {"path": "../BSP/VI4302 API/src/VI4302_System.c"}, {"path": "../BSP/VI4302 API/src/A2_Configurable.c"}, {"path": "../BSP/VI4302 API/BIN/fw_44_00_00_80_R00.c"}], "folders": []}, {"name": "FWLB", "files": [{"path": "../firmware/n32g401_std_periph_driver/src/misc.c"}, {"path": "../firmware/n32g401_std_periph_driver/src/n32g401_adc.c"}, {"path": "../firmware/n32g401_std_periph_driver/src/n32g401_beeper.c"}, {"path": "../firmware/n32g401_std_periph_driver/src/n32g401_comp.c"}, {"path": "../firmware/n32g401_std_periph_driver/src/n32g401_crc.c"}, {"path": "../firmware/n32g401_std_periph_driver/src/n32g401_dbg.c"}, {"path": "../firmware/n32g401_std_periph_driver/src/n32g401_dma.c"}, {"path": "../firmware/n32g401_std_periph_driver/src/n32g401_exti.c"}, {"path": "../firmware/n32g401_std_periph_driver/src/n32g401_flash.c"}, {"path": "../firmware/n32g401_std_periph_driver/src/n32g401_gpio.c"}, {"path": "../firmware/n32g401_std_periph_driver/src/n32g401_i2c.c"}, {"path": "../firmware/n32g401_std_periph_driver/src/n32g401_iwdg.c"}, {"path": "../firmware/n32g401_std_periph_driver/src/n32g401_lptim.c"}, {"path": "../firmware/n32g401_std_periph_driver/src/n32g401_pwr.c"}, {"path": "../firmware/n32g401_std_periph_driver/src/n32g401_rcc.c"}, {"path": "../firmware/n32g401_std_periph_driver/src/n32g401_rtc.c"}, {"path": "../firmware/n32g401_std_periph_driver/src/n32g401_spi.c"}, {"path": "../firmware/n32g401_std_periph_driver/src/n32g401_tim.c"}, {"path": "../firmware/n32g401_std_periph_driver/src/n32g401_usart.c"}, {"path": "../firmware/n32g401_std_periph_driver/src/n32g401_wwdg.c"}], "folders": []}, {"name": "CMSIS", "files": [{"path": "../firmware/CMSIS/device/system_n32g401.c"}], "folders": []}, {"name": "STARTUP", "files": [{"path": "../firmware/CMSIS/device/startup/startup_n32g401.s"}], "folders": []}, {"name": "docs", "files": [{"path": "../docs/Read_Me.txt"}, {"path": "../docs/README.md"}], "folders": []}]}, "outDir": "build", "deviceName": "N32G401K8Q7-2", "packDir": ".pack/Nations/N32G401_DFP.1.1.0", "miscInfo": {"uid": "b7f04289c014a79c3c5fa13a6e658a2f"}, "targets": {"N32G401": {"excludeList": [], "toolchain": "AC5", "compileConfig": {"cpuType": "Cortex-M4", "floatingPointHardware": "single", "scatterFilePath": "", "useCustomScatterFile": false, "storageLayout": {"RAM": [{"tag": "RAM", "id": 1, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "noInit": false}, {"tag": "RAM", "id": 2, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "noInit": false}, {"tag": "RAM", "id": 3, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "noInit": false}, {"tag": "IRAM", "id": 1, "mem": {"startAddr": "0x20000000", "size": "0x4000"}, "isChecked": true, "noInit": false}, {"tag": "IRAM", "id": 2, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "noInit": false}], "ROM": [{"tag": "ROM", "id": 1, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "isStartup": false}, {"tag": "ROM", "id": 2, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "isStartup": false}, {"tag": "ROM", "id": 3, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "isStartup": false}, {"tag": "IROM", "id": 1, "mem": {"startAddr": "0x8000000", "size": "0x10000"}, "isChecked": true, "isStartup": true}, {"tag": "IROM", "id": 2, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "isStartup": false}]}, "options": "null", "archExtensions": ""}, "uploader": "JLink", "uploadConfig": {"bin": "", "baseAddr": "", "cpuInfo": {"vendor": "Nationstech", "cpuName": "N32G401K8"}, "proType": 1, "speed": 8000, "otherCmds": ""}, "uploadConfigMap": {}, "custom_dep": {"name": "default", "incList": ["inc", ".", "../firmware/CMSIS/device/startup", "../firmware/CMSIS/device", "../firmware/CMSIS/core", "../firmware/n32g401_std_periph_driver/inc", "../BSP", "../BSP/cmd_handle", "../BSP/flash", "../BSP/uartfifo", "../BSP/VI4302 API/BIN", "../BSP/VI4302 API/inc", "../BSP/app", "../BSP/work_mode", ".cmsis/include", "RTE/_N32G401"], "libList": [], "defineList": ["N32G401", "USE_STDPERIPH_DRIVER"]}, "builderOptions": {"AC5": {"version": 4, "beforeBuildTasks": [], "afterBuildTasks": [{"name": "[Copy linker output for Keil User Commands]", "command": "mkdir ${KEIL_OUTPUT_DIR} & copy \"${OutDir}\\${ProjectName}.axf\" \"${KEIL_OUTPUT_DIR}\\${ProjectName}.axf\"", "disable": false, "abortAfterFailed": true}, {"name": "fromelf --bin --output=.\\bin\\RCC_ClockConfig.bin .\\Objects\\RCC_ClockConfig.axf", "command": "fromelf --bin --output=.\\bin\\RCC_ClockConfig.bin .\\Objects\\RCC_ClockConfig.axf", "disable": false, "abortAfterFailed": true}], "global": {"use-microLIB": true, "output-debug-info": "enable"}, "c/cpp-compiler": {"optimization": "level-0", "one-elf-section-per-function": true, "c99-mode": true, "C_FLAGS": "--diag_suppress=1 --diag_suppress=1295", "CXX_FLAGS": "--diag_suppress=1 --diag_suppress=1295", "warnings": "all-warnings", "gnu-extensions": true}, "asm-compiler": {}, "linker": {"output-format": "elf", "xo-base": "", "ro-base": "0x00000000", "rw-base": "0x20000000"}}}}, "APP": {"compileConfig": {"cpuType": "Cortex-M4", "floatingPointHardware": "single", "scatterFilePath": "", "useCustomScatterFile": false, "storageLayout": {"RAM": [{"tag": "RAM", "id": 1, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "noInit": false}, {"tag": "RAM", "id": 2, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "noInit": false}, {"tag": "RAM", "id": 3, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "noInit": false}, {"tag": "IRAM", "id": 1, "mem": {"startAddr": "0x20000000", "size": "0x4000"}, "isChecked": true, "noInit": false}, {"tag": "IRAM", "id": 2, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "noInit": false}], "ROM": [{"tag": "ROM", "id": 1, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "isStartup": false}, {"tag": "ROM", "id": 2, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "isStartup": false}, {"tag": "ROM", "id": 3, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "isStartup": false}, {"tag": "IROM", "id": 1, "mem": {"startAddr": "0x8004000", "size": "0xc000"}, "isChecked": true, "isStartup": true}, {"tag": "IROM", "id": 2, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "isStartup": false}]}, "options": "null"}, "uploader": "JLink", "uploadConfig": {"bin": "", "baseAddr": "", "cpuInfo": {"vendor": "null", "cpuName": "null"}, "proType": 1, "speed": 8000, "otherCmds": ""}, "uploadConfigMap": {}, "builderOptions": {"AC5": {"version": 4, "beforeBuildTasks": [], "afterBuildTasks": [{"name": "[Copy linker output for Keil User Commands]", "command": "mkdir ${KEIL_OUTPUT_DIR} & copy \"${OutDir}\\${ProjectName}.axf\" \"${KEIL_OUTPUT_DIR}\\${KEIL_OUTPUT_NAME}.axf\"", "disable": false, "abortAfterFailed": true}, {"name": "fromelf --bin --output=.\\bin\\RCC_ClockConfig.bin .\\Objects\\RCC_ClockConfig.axf", "command": "fromelf --bin --output=.\\bin\\RCC_ClockConfig.bin .\\Objects\\RCC_ClockConfig.axf", "disable": false, "abortAfterFailed": true}], "global": {"use-microLIB": true, "output-debug-info": "enable"}, "c/cpp-compiler": {"optimization": "level-0", "one-elf-section-per-function": true, "c99-mode": true, "C_FLAGS": "--diag_suppress=1 --diag_suppress=1295", "CXX_FLAGS": "--diag_suppress=1 --diag_suppress=1295", "warnings": "all-warnings"}, "asm-compiler": {}, "linker": {"output-format": "elf", "xo-base": "", "ro-base": "0x00000000", "rw-base": "0x20000000"}}}, "toolchain": "AC5", "custom_dep": {"name": "default", "incList": ["inc", ".", "../firmware/CMSIS/device/startup", "../firmware/CMSIS/device", "../firmware/CMSIS/core", "../firmware/n32g401_std_periph_driver/inc", "../BSP", "../BSP/cmd_handle", "../BSP/flash", "../BSP/uartfifo", "../BSP/VI4302 API/BIN", "../BSP/VI4302 API/inc", ".cmsis/include", "RTE/_APP"], "defineList": ["N32G401", "USE_STDPERIPH_DRIVER", "BOOT_APP", "WDT_DEF"], "libList": []}, "excludeList": []}, "APP_NO_WDT": {"compileConfig": {"cpuType": "Cortex-M4", "floatingPointHardware": "single", "scatterFilePath": "", "useCustomScatterFile": false, "storageLayout": {"RAM": [{"tag": "RAM", "id": 1, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "noInit": false}, {"tag": "RAM", "id": 2, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "noInit": false}, {"tag": "RAM", "id": 3, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "noInit": false}, {"tag": "IRAM", "id": 1, "mem": {"startAddr": "0x20000000", "size": "0x4000"}, "isChecked": true, "noInit": false}, {"tag": "IRAM", "id": 2, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "noInit": false}], "ROM": [{"tag": "ROM", "id": 1, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "isStartup": false}, {"tag": "ROM", "id": 2, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "isStartup": false}, {"tag": "ROM", "id": 3, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "isStartup": false}, {"tag": "IROM", "id": 1, "mem": {"startAddr": "0x8004000", "size": "0xc000"}, "isChecked": true, "isStartup": true}, {"tag": "IROM", "id": 2, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "isStartup": false}]}, "options": "null"}, "uploader": "JLink", "uploadConfig": {"bin": "", "baseAddr": "", "cpuInfo": {"vendor": "null", "cpuName": "null"}, "proType": 1, "speed": 8000, "otherCmds": ""}, "uploadConfigMap": {}, "builderOptions": {"AC5": {"version": 4, "beforeBuildTasks": [], "afterBuildTasks": [{"name": "[Copy linker output for Keil User Commands]", "command": "mkdir ${KEIL_OUTPUT_DIR} & copy \"${OutDir}\\${ProjectName}.axf\" \"${KEIL_OUTPUT_DIR}\\${KEIL_OUTPUT_NAME}.axf\"", "disable": false, "abortAfterFailed": true}, {"name": "fromelf --bin --output=.\\bin\\RCC_ClockConfig.bin .\\Objects\\RCC_ClockConfig.axf", "command": "fromelf --bin --output=.\\bin\\RCC_ClockConfig.bin .\\Objects\\RCC_ClockConfig.axf", "disable": false, "abortAfterFailed": true}], "global": {"use-microLIB": true, "output-debug-info": "enable"}, "c/cpp-compiler": {"optimization": "level-0", "one-elf-section-per-function": true, "c99-mode": true, "C_FLAGS": "--diag_suppress=1 --diag_suppress=1295", "CXX_FLAGS": "--diag_suppress=1 --diag_suppress=1295", "warnings": "all-warnings"}, "asm-compiler": {}, "linker": {"output-format": "elf", "xo-base": "", "ro-base": "0x00000000", "rw-base": "0x20000000"}}}, "toolchain": "AC5", "custom_dep": {"name": "default", "incList": ["inc", ".", "../firmware/CMSIS/device/startup", "../firmware/CMSIS/device", "../firmware/CMSIS/core", "../firmware/n32g401_std_periph_driver/inc", "../BSP", "../BSP/cmd_handle", "../BSP/flash", "../BSP/uartfifo", "../BSP/VI4302 API/BIN", "../BSP/VI4302 API/inc", ".cmsis/include", "RTE/_APP_NO_WDT"], "defineList": ["N32G401", "USE_STDPERIPH_DRIVER", "BOOT_APP"], "libList": []}, "excludeList": []}}, "version": "3.6"}